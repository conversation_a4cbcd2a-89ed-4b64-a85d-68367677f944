/**
 * Tests for the refactored TemplateComparator
 * Verifies that the unified comparison system works correctly
 */

import { beforeEach, describe, expect, it, vi } from "vitest";

import { TemplateComparator } from "../template-comparator.js";

// Mock the dependencies
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    fileExists: vi.fn(),
    readFile: vi.fn(),
  })),
}));

vi.mock("../logger.js", () => ({
  Logger: {
    info: vi.fn(),
    warning: vi.fn(),
  },
}));

vi.mock("@openai/agents", () => ({
  Agent: vi.fn(),
  run: vi.fn(),
}));

describe("TemplateComparator", () => {
  let comparator: TemplateComparator;
  let mockFsManager: any;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create a new instance for each test
    comparator = new TemplateComparator();

    // Get the mocked filesystem manager
    mockFsManager = (comparator as any).fsManager;
  });

  describe("compareWithTemplate", () => {
    it("should handle empty file list", async () => {
      const filesToCompare: Array<{ filePath: string; content: string; prompt: string }> = [];

      const result = await comparator.compareWithTemplate(filesToCompare);
      expect(result).toEqual([]);
    });

    it("should process files using unified comparison method", async () => {
      const filesToCompare = [
        {
          filePath: "/test/project/package.json",
          content: '{"name": "test", "version": "1.0.0"}',
          prompt: "This is a package.json file containing project metadata and dependencies.",
        },
      ];

      // Mock template file doesn't exist to avoid actual comparison
      mockFsManager.fileExists.mockReturnValue(false);

      const result = await comparator.compareWithTemplate(filesToCompare);
      expect(result).toEqual([]);
      expect(mockFsManager.fileExists).toHaveBeenCalled();
    });

    it("should handle files that exist in template", async () => {
      const filesToCompare = [
        {
          filePath: "/test/project/package.json",
          content: '{"name": "test", "version": "1.0.0"}',
          prompt: "This is a package.json file containing project metadata and dependencies.",
        },
      ];

      // Mock template file exists
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue('{"name": "template", "version": "2.0.0"}');

      const result = await comparator.compareWithTemplate(filesToCompare);

      // Should return a comparison result (even if empty differences due to no LLM)
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        file: "package.json",
        requiresUpdate: false, // No differences without LLM
        updatePriority: "optional",
      });
    });
  });

  describe("path resolution", () => {
    it("should resolve package.json path correctly", () => {
      const templatePath = (comparator as any).resolveTemplatePath("/project/package.json");
      expect(templatePath).toContain("package.json");
    });

    it("should resolve src files correctly", () => {
      const templatePath = (comparator as any).resolveTemplatePath("/project/src/main.tsx");
      expect(templatePath).toContain("src");
      expect(templatePath).toContain("main.tsx");
    });

    it("should handle root configuration files", () => {
      const templatePath = (comparator as any).resolveTemplatePath("/project/tsconfig.json");
      expect(templatePath).toContain("tsconfig.json");
    });
  });

  describe("file path utilities", () => {
    it("should extract relative paths correctly", () => {
      const relativePath = (comparator as any).getRelativeFilePath("/project/src/components/App.tsx");
      expect(relativePath).toBe("src/components/App.tsx");
    });

    it("should handle root files", () => {
      const relativePath = (comparator as any).getRelativeFilePath("/project/package.json");
      expect(relativePath).toBe("package.json");
    });
  });

  describe("language detection", () => {
    it("should detect TypeScript files", () => {
      const language = (comparator as any).getLanguageFromExtension("ts");
      expect(language).toBe("typescript");
    });

    it("should detect JSON files", () => {
      const language = (comparator as any).getLanguageFromExtension("json");
      expect(language).toBe("json");
    });

    it("should handle unknown extensions", () => {
      const language = (comparator as any).getLanguageFromExtension("unknown");
      expect(language).toBe("");
    });
  });
});
