/**
 * Template comparator using LLM integration
 * Compares project files against the latest template to identify differences
 */

import { dirname, join } from "path";
import { fileURLToPath } from "url";

import { Agent, run } from "@openai/agents";

import type { FileDifference, ProjectFile, TemplateComparisonResult } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class TemplateComparator {
  private fsManager: FileSystemManager;
  private agent?: Agent;
  private templatePath: string;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Get template path
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    this.templatePath = join(__dirname, "..", "..", "templates", "basic");

    // Initialize the LLM agent for template comparison only if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "TemplateComparator",
        instructions: `You are an expert TypeScript/React developer.
          Your task is to compare project files against the latest template and identify differences.

          When comparing files, identify:
          1. Dependency version differences
          2. Configuration changes
          3. Structural differences
          4. Missing or outdated patterns
          5. Custom modifications that should be preserved

          For each difference, specify:
          - Type: dependency, configuration, code, or structure
          - Description: clear explanation of the difference
          - Template value: what the template has
          - Project value: what the project currently has
          - Action: add, update, remove, or merge
          - Priority: critical, recommended, or optional

          Return your analysis as a structured JSON object with the differences array.`,
        model: process.env.OPENAI_MODEL || "gpt-4o",
      });
    }
  }

  /**
   * Compare project against template using a configured list of files
   */
  async compareWithTemplate(filesToCompare: ProjectFile[]): Promise<TemplateComparisonResult[]> {
    Logger.info("Comparing project with latest template...");

    const comparisons: TemplateComparisonResult[] = [];

    // Compare each file using the unified comparison method
    for (const file of filesToCompare) {
      const comparison = await this.compare(file.filePath, file.content, file.prompt);
      if (comparison) {
        comparisons.push(comparison);
      }
    }

    return comparisons;
  }

  /**
   * Unified comparison method that handles any file type intelligently
   * Uses LLM to dynamically analyze and compare files based on their content
   */
  private async compare(
    projectFilePath: string,
    projectContent: string,
    contextPrompt: string,
  ): Promise<TemplateComparisonResult | null> {
    // Resolve template file path
    const templateFilePath = this.resolveTemplatePath(projectFilePath);

    if (!this.fsManager.fileExists(templateFilePath)) {
      // File doesn't exist in template, might be custom or not relevant
      return null;
    }

    try {
      const templateContent = await this.fsManager.readFile(templateFilePath);

      // Get LLM-driven differences analysis with contextual prompt
      const differences = await this.compareWithLLM(
        projectContent,
        templateContent,
        projectFilePath,
        templateFilePath,
        contextPrompt,
      );

      // Extract relative file path for result
      const relativePath = this.getRelativeFilePath(projectFilePath);

      return {
        file: relativePath,
        templateContent,
        projectContent,
        differences,
        requiresUpdate: differences.length > 0,
        updatePriority: this.determineUpdatePriority(differences),
      };
    } catch (error) {
      Logger.warning(
        `Failed to compare ${projectFilePath}: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      return null;
    }
  }

  /**
   * Resolve template file path based on project file path
   */
  private resolveTemplatePath(projectFilePath: string): string {
    // Extract filename and directory structure
    const fileName = projectFilePath.split(/[/\\]/).pop() || "";

    // Handle common file patterns and their template locations
    if (fileName === "package.json") {
      return join(this.templatePath, "package.json");
    }

    // Handle src directory files
    if (projectFilePath.includes("src/") || projectFilePath.includes("src\\")) {
      const srcIndex = Math.max(projectFilePath.indexOf("src/"), projectFilePath.indexOf("src\\"));
      const relativePath = projectFilePath.substring(srcIndex);
      return join(this.templatePath, relativePath);
    }

    // Handle root configuration files
    return join(this.templatePath, fileName);
  }

  /**
   * Get relative file path for display purposes
   */
  private getRelativeFilePath(projectFilePath: string): string {
    // Extract meaningful relative path
    const fileName = projectFilePath.split(/[/\\]/).pop() || "";

    if (projectFilePath.includes("src/") || projectFilePath.includes("src\\")) {
      const srcIndex = Math.max(projectFilePath.indexOf("src/"), projectFilePath.indexOf("src\\"));
      return projectFilePath.substring(srcIndex);
    }

    return fileName;
  }

  /**
   * Enhanced LLM-driven comparison that intelligently analyzes any file type
   */
  private async compareWithLLM(
    projectContent: string,
    templateContent: string,
    projectFilePath: string,
    templateFilePath: string,
    fileUpdateGuidance: string,
  ): Promise<FileDifference[]> {
    // If no agent is available, return empty array
    if (!this.agent) {
      Logger.warning("LLM comparison not available without OpenAI API key. Using basic comparison.");
      return [];
    }

    const fileName = projectFilePath.split(/[/\\]/).pop() || "";
    const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";

    // Create an intelligent prompt that provides context for any file type
    const prompt = `You are an expert developer analyzing files and update files to latest version. Compare these two files and identify meaningful differences.

PROJECT FILE (old version): ${fileName}
Path: ${projectFilePath}
\`\`\`${this.getLanguageFromExtension(fileExtension)}
${projectContent}
\`\`\`

TEMPLATE FILE (latest version): ${fileName}
Path: ${templateFilePath}
\`\`\`${this.getLanguageFromExtension(fileExtension)}
${templateContent}
\`\`\`

ANALYSIS INSTRUCTIONS:
1. Use the file context above to understand the specific purpose and expected changes for this file type
2. Based on the contextual guidance, focus on the most relevant aspects for this specific file
3. Identify differences that matter for:
   - Functionality and behavior
   - Module dependencies and compatibility (particularly @cscs-agent/core, @cscs-agent/presets and @cscs-agent/icons)
   - Configuration and setup
   - Code structure and patterns
   - Security and best practices

4. Preserve custom user modifications while identifying template improvements
5. Focus on actionable differences that can be applied as guided by the file context
6. Add necessary imports, remove unused imports.
7. ${fileUpdateGuidance}

Return a JSON array of differences with this exact structure:
[
  {
    "type": "dependency|configuration|code|structure",
    "description": "Clear description of the difference and its impact",
    "templateValue": "value from template (or description if complex)",
    "projectValue": "value from project (or description if complex)",
    "action": "add|update|remove|merge"
  }
]

Only include differences that are meaningful and actionable. Ignore trivial formatting differences unless they affect functionality.`;

    return await this.getLLMDifferences(prompt);
  }

  /**
   * Get appropriate language identifier for code blocks based on file extension
   */
  private getLanguageFromExtension(extension: string): string {
    const languageMap: Record<string, string> = {
      ts: "typescript",
      tsx: "typescript",
      js: "javascript",
      jsx: "javascript",
      json: "json",
      md: "markdown",
      yml: "yaml",
      yaml: "yaml",
      toml: "toml",
      css: "css",
      scss: "scss",
      html: "html",
      xml: "xml",
    };

    return languageMap[extension] || "";
  }

  /**
   * Get differences from LLM response or provide basic comparison
   */
  private async getLLMDifferences(prompt: string): Promise<FileDifference[]> {
    // If no agent is available, return empty array (basic comparison will be used)
    if (!this.agent) {
      Logger.warning("LLM comparison not available without OpenAI API key. Using basic comparison.");
      return [];
    }

    try {
      const result = await run(this.agent, prompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        return [];
      }

      // Try to extract JSON from the response
      const jsonMatch = result.finalOutput.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        Logger.warning("No valid JSON found in LLM response");
        return [];
      }

      const differences: FileDifference[] = JSON.parse(jsonMatch[0]);
      console.log(result.finalOutput);
      return differences;
    } catch (error) {
      Logger.warning(`Failed to get LLM differences: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    }
  }

  /**
   * Determine update priority based on differences
   */
  private determineUpdatePriority(differences: FileDifference[]): "critical" | "recommended" | "optional" {
    const hasCritical = differences.some(
      (diff) =>
        (diff.type === "dependency" && diff.action === "update") ||
        diff.type === "structure" ||
        diff.description.toLowerCase().includes("security") ||
        diff.description.toLowerCase().includes("breaking"),
    );

    if (hasCritical) {
      return "critical";
    }

    const hasRecommended = differences.some((diff) => diff.type === "dependency" || diff.type === "configuration");

    return hasRecommended ? "recommended" : "optional";
  }
}
